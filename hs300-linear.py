# 导入函数库
from jqdata import *
from datetime import datetime, timedelta
import pandas as pd
import math
from sortedcontainers import SortedDict
import numpy as np




class Node:
    def __init__(self, date, val):
        self.date = date
        self.val = val

    def __repr__(self):
        return f"Node(date={self.date}, val={self.val})"

class Zset:
    def __init__(self, series, percentile1 = 0.3, percentile2 = 0.7):
        self.nodes = SortedDict()
        self.val_nodes_length = SortedDict()
        self.total_nodes = 0
        self.date_index = {}
        self.percentile1 = percentile1 
        self.percentile2 = percentile2
        
        self.percent_index1 = None 
        self.percent_index2 = None
        
        for k, v in series.items():
            node = Node(k, v)
            self.insert(node)

    def _update_val_nodes_length(self, key, delta):
        if key not in self.val_nodes_length :
            self.val_nodes_length[key] = 0
        
        self.val_nodes_length[key] += delta
        self.total_nodes += delta

    def insert(self, node):
        # print(node.date, node.val)
        # print(type(node.date), type(node.val))
        # print("nodes len: {}".format(len(self.nodes)))
        
        # 插入节点到SortedDict中，保持有序
        if node.val not in self.nodes:
            self.nodes[node.val] = []
        self.nodes[node.val].append(node)
        # 同时更新date_index以实现O(1)查询
        self.date_index[node.date] = node
        
        # val = node.val
        # if val not in self.nodes:
        #     self.nodes[val] = []
        # self.nodes[val].append(node)
        # # 同时更新date_index以实现O(1)查询
        # self.date_index[node.date] = node
        
        self._update_val_nodes_length(node.val, 1)
    

        
    
    # def update_percentiles(self):
        
    #     total_nodes = len(self.allNodes)
    #     self.percent_index1 = int(self.percentile1 * total_nodes)
    #     self.percent_index2 = int(self.percentile2 * total_nodes)
        

    def delete(self, date):
        # 通过date查询并删除节点
        if date in self.date_index:
            node = self.date_index[date]
            # 从SortedDict中删除节点
            self.nodes[node.val].remove(node)
            self._update_val_nodes_length(node.val, -1)
            if not self.nodes[node.val]:  # 如果该val下没有其他节点，删除该val键
                del self.nodes[node.val]
                del self.val_nodes_length[node.val]
            del self.date_index[date]
            
            # self.update_percentiles()
    
    def get_percentiles(self):
        
        self.percent_index1 = int(self.percentile1 * self.total_nodes)
        self.percent_index2 = int((1 - self.percentile2) * self.total_nodes) # 用于反向遍历
        
        percentile1_val = 0
        percentile2_val = 0 
        
        cumulative_index = 0
        for val, length in self.val_nodes_length.items(): 
            cumulative_index += length
            
            if cumulative_index > self.percent_index1:
                break

            percentile1_val = val
        
        cumulative_index = 0
        for val, length in reversed(self.val_nodes_length.items()):
            cumulative_index += length
            
            if cumulative_index > self.percent_index2:
                break
            
            percentile2_val = val
        
        return percentile1_val, percentile2_val
        
    # def get_percentile(self):
        
    #     return self.all_nodes[self.percent_index1].val, self.all_nodes[self.percent_index2].val

    def __repr__(self):
        return f"Zset(nodes={self.nodes}, date_index={self.date_index})"


def sigmoid(x):
    return 1 / (1 + np.exp(-x))

# 初始化函数，设定基准等等
def initialize(context):
    # 设定基准
    set_benchmark('000300.XSHG')
    # 开启动态复权模式(真实价格)
    set_option("use_real_price", True)

    ### 场外基金相关设定 ###
    # 设置账户类型: 场外基金账户
    set_subportfolios([SubPortfolioConfig(context.portfolio.cash, 'open_fund')])
    # 设置赎回到账日
    set_redeem_latency(4, 'stock_fund')
    
    cur_date = context.current_dt
    
    first_start_date = cur_date - timedelta(days=5 * 365)
    first_start_date = first_start_date.strftime('%Y-%m-%d')
    
    # 维护沪深300历史PE，PB数据
    pe_pb_que = cal_index_pe_pb('000300.XSHG', first_start_date, cur_date)
    pe_pb_que = pe_pb_que[['pb_ttm_index', 'pe_ttm_index']]
    
    g.pe_cache = Zset(pe_pb_que['pe_ttm_index'])
    g.pb_cache = Zset(pe_pb_que['pb_ttm_index'])
    
    # del pe_pb_que
    
    
    g.pe_today = None
    g.pb_today = None
    
    g.pe_danger = None
    g.pb_danger = None
    
    allcash = context.portfolio.available_cash
    g.cash_unit = allcash / (5*12*4) #5年窗口，以周为频率
    
    ## 运行函数（reference_security为运行时间的参考标的；传入的标的只做种类区分，因此传入'000300.XSHG'或'510300.XSHG'是一样的）
        # 开盘时运行
    run_daily(market_open, time='open', reference_security='000300.XSHG')
        # 收盘后运行
    run_daily(after_market_close, time='after_close', reference_security='000300.XSHG')

def cal_index_pe_pb(code, start_date, end_date): 
    index_data = get_price(code, start_date = start_date, end_date = end_date, frequency='daily', fields=None, skip_paused=False, fq='pre', count=None, panel=False, fill_paused=True)
    time_list = index_data.index.tolist()
    df = get_valuation(code, start_date=None, end_date = end_date, fields=['pe_ratio','pb_ratio','market_cap'], count=1)
    for date in time_list:
    #获取成份股
        stock_list = get_index_stocks(code, date = date)
        data = get_valuation(stock_list, start_date=None, end_date= date, fields=['pe_ratio','pb_ratio','market_cap'], count=1)
        df = pd.concat([df,data]).reset_index(drop = True)
        #time.sleep(0.05)
    df = df.drop_duplicates()
    df['net_income_ttm'] = df['market_cap']/df['pe_ratio']
    df['book_value'] = df['market_cap']/df['pb_ratio']
    valuation = df.groupby('day').sum()
    valuation['pe_ttm_index'] = valuation['market_cap']/valuation['net_income_ttm']
    valuation['pb_ttm_index'] = valuation['market_cap']/valuation['book_value']
    return valuation

def market_open(context):
    
    # 确定时间是周几
    weekday = context.current_dt.isoweekday()
    log.info("今天是周 %s" % weekday)
    
    
    
    # 设置场外基金标的为景顺沪深300('000311.OF')
    s = '519671.OF'
    
    today = context.current_dt

    # 获取基金信息
    fund_info = get_fund_info(s)
    log.info("基金名称：",fund_info['fund_name'])

    
    
    "PE+PB+风险溢价判断"
    # 获取基金的市值表数据，包括PE
    # df_valuation = get_valuation(fund_code, start_date=start_date, end_date=end_date, fields=['pe_ratio'])
    pe_pb_ttm_today = cal_index_pe_pb('000300.XSHG', today, today)
    pe_pb_ttm_today = pe_pb_ttm_today[['pb_ttm_index', 'pe_ttm_index']]
    g.pb_today = pe_pb_ttm_today['pb_ttm_index'][0]
    g.pe_today = pe_pb_ttm_today['pe_ttm_index'][0]
    # print(type(pe_today))
    
    "TODO: 判断当前PE，PB的分位值，执行购买逻辑，并更新que"
    drop_date = today - timedelta(days=5 * 365)
    drop_date = drop_date.strftime('%Y-%m-%d')
    today = today.strftime('%Y-%m-%d')
    
    node_pe = Node(today, g.pe_today)
    node_pb = Node(today, g.pb_today)
    
    g.pe_cache.insert(node_pe)
    g.pe_cache.delete(drop_date)
    
    g.pb_cache.insert(node_pb)
    g.pb_cache.delete(drop_date)
    
    pe_chance, g.pe_danger = g.pe_cache.get_percentiles()
    pb_chance, g.pb_danger = g.pb_cache.get_percentiles()
    
    pe_pb_info = today + " PE: {} ".format(g.pe_today) + "PB: {} ".format(g.pb_today)
    log.info(pe_pb_info)
    log.info("PE_CHANCE: {} ".format(pe_chance) + "PB_CHANCE: {}".format(pb_chance))
    
    # if weekday != 4 : 
    #     return
    "TODO: 将if else信号改成线性信号，用线性信号控制仓位xian'xing投入"

    delta_pe = pe_chance - g.pe_today
    delta_pb = pb_chance - g.pb_today
    
    delta_pe = (math.exp(delta_pe) - 1) if delta_pe > 0 else -1
    delta_pb = (math.exp(delta_pb) - 1) if delta_pb > 0 else -1
    
    if delta_pb == -1 and delta_pe == -1:
        return
    
    w_pe = 0.5
    w_pb = 0.5
    
    add_position = w_pe * sigmoid(delta_pe) + w_pe * sigmoid(delta_pb)
    add_cash = (1 + add_position) * g.cash_unit
    
     # print(s)
    log.info("****************BUY**********************")
    o = purchase(s, add_cash)
    log.info(o)
    s = today + " PE: {} ".format(g.pe_today) + "PB: {} ".format(g.pb_today)
    log.info(s)
    log.info("PE_CHANCE: {} ".format(pe_chance) + "PB_CHANCE: {}".format(pb_chance))
    
    # if g.pe_today <= pe_chance or g.pb_today <= pb_chance :
    #     # o = order_value(s, 100)
    #     # print(s)
    #     log.info("****************BUY**********************")
    #     o = purchase(s, 100)
    #     log.info(o)
    #     s = today + " PE: {} ".format(g.pe_today) + "PB: {} ".format(g.pb_today)
    #     log.info(s)
    #     log.info("PE_CHANCE: {} ".format(pe_chance) + "PB_CHANCE: {}".format(pb_chance))
    
    # # 申购基金
    # if weekday == 1:
    #     o = purchase(s, 10000)
    #     log.info(o)
    # # 赎回基金
    # elif weekday == 3:
    #     o1 = redeem(s, 4000)
    #     log.info(o1)
    # elif weekday == 4:
    #     o2 = redeem(s, 3000)
    #     log.info(o2)

## 收盘后运行函数
def after_market_close(context):
    
    s = '519671.OF'
    weekday = context.current_dt.isoweekday()
    # if weekday != 3 : 
    #     return 
    
    # pe_chance, pe_danger = g.pe_cache.get_percentile()
    # pb_chance, pb_danger = g.pb_cache.get_percentile()
    
    pe_pb_info = context.current_dt.strftime('%Y-%m-%d') + " PE: {} ".format(g.pe_today) + "PB: {} ".format(g.pb_today)
    log.info(pe_pb_info)
    log.info("PE_DANGER: {} ".format(g.pe_danger) + "PB_DANGER: {}".format(g.pb_danger))
    
    closeable_amout = 0
    for f in context.portfolio.positions: 
        closeable_amout = context.portfolio.positions[f].closeable_amount
    delta_pe = g.pe_today - g.pe_danger
    delta_pb = g.pb_today - g.pb_danger
    
    delta_pe = (math.exp(delta_pe) - 1) if delta_pe > 0 else -1
    delta_pb = (math.exp(delta_pb) - 1) if delta_pb > 0 else -1
    
    if delta_pb == -1 and delta_pe == -1:
        return
    
    w_pe = 0.5
    w_pb = 0.5
    
    cut_position = w_pe * sigmoid(delta_pe) + w_pe * sigmoid(delta_pb)
    # cut_cash = cut_position * closeable_amout
    cut_cash = (1 + cut_position) * g.cash_unit
    
    log.info("****************SELL*************************")
    o = redeem(s, cut_cash)
    log.info(o)
    pe_pb_info = context.current_dt.strftime('%Y-%m-%d') + " PE: {} ".format(g.pe_today) + "PB: {} ".format(g.pb_today)
    log.info(pe_pb_info)
    log.info("PE_DANGER: {} ".format(g.pe_danger) + "PB_DANGER: {}".format(g.pb_danger))
    
    # if g.pe_today > g.pe_danger or g.pb_today > g.pb_danger :
    #     # o = order_value(s, -100)
    #     log.info("****************SELL*************************")
    #     o = redeem(s, 100)
    #     log.info(o)
    #     pe_pb_info = context.current_dt.strftime('%Y-%m-%d') + " PE: {} ".format(g.pe_today) + "PB: {} ".format(g.pb_today)
    #     log.info(pe_pb_info)
    #     log.info("PE_DANGER: {} ".format(g.pe_danger) + "PB_DANGER: {}".format(g.pb_danger))
    
    # 查看融资融券账户相关相关信息(更多请见API-对象-SubPortfolio)
    p = context.portfolio.subportfolios[0]
    log.info('- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -')
    log.info('查看场外基金账户相关相关信息(更多请见API-对象-SubPortfolio)：')
    log.info('场外基金持有份额：',p.long_positions['519671.OF'].closeable_amount)
    log.info('账户所属类型：',p.type)
    log.info('##############################################################')
    
    
