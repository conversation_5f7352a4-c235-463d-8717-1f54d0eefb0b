# 一、简介
我希望你参考现存的几个脚本，为我编写一个配置沪深三百龙头股票+银行股的对冲策略。策略的灵感来源于做空指数对冲策略，观察到中国A股中银行股常常会作为国家队拉盘、砸盘的工具，走势与指数有来拒去留的趋势，遂设想是否能通过按一定比例配置代表沪深300的龙头股+银行股来对冲掉沪深300的波动。

# 二、任务要求
策略的具体操作如下：
1. 每周选择沪深300市值最大的3只股票作为龙头股，同时选择3只市值最大的银行股作为对冲股。选股在每周一更新。
2. 进场条件：（每日检测）
    龙头股：该股日频K线中：5日均线向上穿过20日均线，且MACD金叉，MACD取默认参数。在
    银行股：沪深300日频K线KDJ死叉，KDJ取默认参数
3. 仓位管理：
    仓位权重：龙头股：银行股 = 5：5，且权重保留一个变量用于随时控制调参测试
    买入交易规模：每周一计算当前可用现金cash_week，每一天最多允许买入cash_week / 5价格的股票
4. 出场条件：（每日检测）
    龙头股：该股日频K线中：5日均线向下穿过20日均线，且MACD死叉
    银行股：沪深300日频K线KDJ金叉





# 三、其他要求
1. 不要引入jointquant的一些自动选股之类的API，因为我要手动进行交易，而不会使用机器交易。理想的交易过程是：（1）我在各大券商、金融平台浏览到沪深300当日的一些技术指标值；（2）我将这些值输入你设计好的操作仓位计算器中，得到今日需要操作的仓位值；（3）我在相应的券商账户上手动操作交易。

2. 我希望你将这个策略设计成一个输入为技术指标，输出为买入/卖出仓位数量的函数，并且这个函数最好不要依赖任何jointquant的API，只要依赖python的开源库就好，以便我将这个函数从回测框架中剥离出来作为操作仓位计算器使用。

3. 可以接受的技术指标有：MA, BOLL, EMA, SAR, KC, 一目均衡表，VWAP，神奇九转以及成交量, MACD, KDJ, ARBR, CR, DMA, EMV, RSI, PE, PB, PE历史分位值, PB历史分位值, 风险溢价, 开盘价, 收盘价, 高位, 低位。它们都是我可以直接从券商APP上肉眼获取的数据。请你根据你所知道的沪深300指数的市场特性，选择合适的指标作为函数的输入。

4. 至于仓位的计算过程，不要引入除了以上输入之外的统计量（如擅自抽取多日内的价格制成一个dataframe并完成一些如均值等操作），牢记我需要你将策略设计成一个操作仓位计算器，计算器的输入和输出的格式是确定的，我无法提供多日的某个数值。此外，尽可能的克服二、现存问题中提到的一些缺陷。

5. 将符合以上要求的操作仓位计算器封装成一个类，在jointquant平台上进行在线回测，在这个类之外你可以随意调用jointquant的API，参考hs300.py里的写法。

6. 仅编写代码，不要尝试在本地运行或者安装任何环境。因为这段代码仅适用于jointquant在线回测平台，且本地也并未安装python环境。
